/**
 * API service for Mail_Auto web portal
 * Connects frontend to Flask backend endpoints
 */

import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api', // Proxied to Flask backend via Vite
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for debugging in development
api.interceptors.request.use(
  (config) => {
    if (import.meta.env.DEV) {
      console.log('🔄 API Request:', config.method?.toUpperCase(), config.url);
    }
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    if (import.meta.env.DEV) {
      console.log('✅ API Response:', response.status, response.config.url);
    }
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

// Type definitions for API responses
export interface HealthStatus {
  status: string;
  timestamp: string;
  processing_active: boolean;
  tenants_count: number;
}

export interface DashboardStats {
  total_documents: number;
  successful_documents: number;
  failed_documents: number;
  active_tenants: number;
  processing_enabled: boolean;
  success_rate: number;
}

export interface RecentActivity {
  id: string;
  tenant: string;
  filename: string;
  document_type: string;
  status: string;
  operation_type: string;
  processing_date: string;
  file_size: number;
  mailbox: string;
  upload_folder: string;
  processing_time_ms?: number;
  error_message?: string;
  notification_recipients?: string[];
  extracted_data?: any;
}

export interface LiveStats {
  total_documents_today: number;
  successful_today: number;
  failed_today: number;
  processing_rate_per_hour: number;
  last_processed: string | null;
  active_tenants: number;
  success_rate_today: number;
}

export interface DocumentType {
  name: string;
  value: number;
  color: string;
}

export interface SystemHealth {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  uptime_hours: number;
  active_tenants: number;
  documents_processed_today: number;
  errors_today: number;
  error_rate: number;
  processing_enabled: boolean;
  last_updated: string;
}

export interface ErrorTrend {
  date: string;
  errors: number;
  total: number;
  error_rate: number;
}

export interface TenantActivity {
  tenant_name: string;
  storage_type: string;
  mailbox_count: number;
  documents_this_week: number;
  successful_this_week: number;
  failed_this_week: number;
  success_rate: number;
  last_activity: string | null;
  status: 'active' | 'inactive' | 'error';
  preferred_language: string;
  tracking_enabled: boolean;
}

export interface ProcessingQueue {
  queue_size: number;
  processing_items: any[];
  completed_today: number;
  failed_today: number;
  average_processing_time: number;
}

export interface ErrorLog {
  id: string;
  tenant: string;
  filename: string;
  document_type: string;
  error_message: string;
  timestamp: string;
  mailbox: string;
  severity: 'error' | 'warning';
}

export interface TenantInfo {
  name: string;
  storage_type: string;
  mailboxes: Array<{
    email: string;
    display_name: string;
    enabled: boolean;
  }>;
  mailbox_count: number;
  recent_documents: number;
  preferred_language: string;
  tracking_enabled: boolean;
}

export interface ProcessingStatus {
  active: boolean;
  thread_alive: boolean;
}

// API functions
export const healthApi = {
  check: (): Promise<HealthStatus> =>
    api.get('/health').then(res => res.data),
};

export const dashboardApi = {
  getStats: (devMode = false): Promise<DashboardStats> =>
    api.get('/dashboard/stats', { params: { dev_mode: devMode } }).then(res => res.data),

  getRecentActivity: (devMode = false): Promise<RecentActivity[]> =>
    api.get('/dashboard/recent-activity', { params: { dev_mode: devMode } }).then(res => res.data),

  getLiveStats: (devMode = false): Promise<LiveStats> =>
    api.get('/dashboard/live-stats', { params: { dev_mode: devMode } }).then(res => res.data),

  getDocumentTypes: (devMode = false): Promise<DocumentType[]> =>
    api.get('/dashboard/document-types', { params: { dev_mode: devMode } }).then(res => res.data),
};

export const adminApi = {
  getTenants: (devMode = false): Promise<TenantInfo[]> =>
    api.get('/admin/tenants', { params: { dev_mode: devMode } }).then(res => res.data),

  getProcessingStatus: (): Promise<ProcessingStatus> =>
    api.get('/admin/processing/status').then(res => res.data),

  startProcessing: (): Promise<{ message: string }> =>
    api.post('/admin/processing/start').then(res => res.data),

  stopProcessing: (): Promise<{ message: string }> =>
    api.post('/admin/processing/stop').then(res => res.data),

  getSystemHealth: (devMode = false): Promise<SystemHealth> =>
    api.get('/admin/system-health', { params: { dev_mode: devMode } }).then(res => res.data),

  getErrorTrends: (devMode = false): Promise<ErrorTrend[]> =>
    api.get('/admin/error-trends', { params: { dev_mode: devMode } }).then(res => res.data),

  getTenantActivity: (devMode = false): Promise<TenantActivity[]> =>
    api.get('/admin/tenant-activity', { params: { dev_mode: devMode } }).then(res => res.data),

  getProcessingQueue: (devMode = false): Promise<ProcessingQueue> =>
    api.get('/admin/processing-queue', { params: { dev_mode: devMode } }).then(res => res.data),

  getErrorLogs: (devMode = false): Promise<ErrorLog[]> =>
    api.get('/admin/error-logs', { params: { dev_mode: devMode } }).then(res => res.data),
};

export const tenantApi = {
  getRecentActivity: (tenantName: string): Promise<RecentActivity[]> =>
    api.get(`/tenant/${tenantName}/recent-activity`).then(res => res.data),
};

// Development helpers
export const devApi = {
  // Test connection to backend
  testConnection: async (): Promise<boolean> => {
    try {
      await healthApi.check();
      return true;
    } catch (error) {
      console.error('Backend connection failed:', error);
      return false;
    }
  },

  // Get all available endpoints for debugging
  getEndpoints: () => {
    return [
      'GET /api/health',
      'GET /api/dashboard/stats',
      'GET /api/dashboard/recent-activity',
      'GET /api/dashboard/live-stats',
      'GET /api/dashboard/document-types',
      'GET /api/tenant/{tenant_name}/recent-activity',
      'GET /api/admin/tenants',
      'GET /api/admin/system-health',
      'GET /api/admin/error-trends',
      'GET /api/admin/tenant-activity',
      'GET /api/admin/processing-queue',
      'GET /api/admin/error-logs',
      'GET /api/admin/processing/status',
      'POST /api/admin/processing/start',
      'POST /api/admin/processing/stop',
    ];
  },
};

export default api;
